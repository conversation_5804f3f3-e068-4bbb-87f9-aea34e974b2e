using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;

namespace WebAPI.Controllers
{
    /// <summary>
    /// <PERSON>ye antrenman ilerleme API controller'ı
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MemberWorkoutProgressController : ControllerBase
    {
        private readonly IMemberWorkoutProgressService _memberWorkoutProgressService;

        public MemberWorkoutProgressController(IMemberWorkoutProgressService memberWorkoutProgressService)
        {
            _memberWorkoutProgressService = memberWorkoutProgressService;
        }

        /// <summary>
        /// Gün ilerleme durumunu toggle eder (tamamla/geri al)
        /// </summary>
        [HttpPost("toggle-day-progress")]
        public IActionResult ToggleDayProgress([FromBody] WorkoutProgressToggleDto toggleDto)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.ToggleDayProgress(toggleDto, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Program ilerlemesini sıfırlar (tüm tikler kaldırılır)
        /// </summary>
        [HttpPost("reset-program-progress")]
        public IActionResult ResetProgramProgress([FromBody] WorkoutProgressResetDto resetDto)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.ResetProgramProgress(resetDto, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Belirli program için ilerleme durumunu getirir
        /// </summary>
        [HttpGet("get-progress-by-program/{memberWorkoutProgramId}")]
        public IActionResult GetProgressByProgram(int memberWorkoutProgramId)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.GetProgressByProgram(memberWorkoutProgramId, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Belirli program için ilerleme özetini getirir
        /// </summary>
        [HttpGet("get-progress-summary/{memberWorkoutProgramId}")]
        public IActionResult GetProgressSummary(int memberWorkoutProgramId)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.GetProgressSummary(memberWorkoutProgramId, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Belirli program için günlük ilerleme durumlarını getirir
        /// </summary>
        [HttpGet("get-day-progress-status/{memberWorkoutProgramId}")]
        public IActionResult GetDayProgressStatus(int memberWorkoutProgramId)
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.GetDayProgressStatus(memberWorkoutProgramId, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// User ID'ye göre aktif programların ilerleme durumunu getirir (mobil API için)
        /// </summary>
        [HttpGet("get-progress-by-user")]
        public IActionResult GetProgressByUserId()
        {
            // Kullanıcının ID'sini token'dan al
            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest("Kullanıcı kimliği bulunamadı.");
            }

            var result = _memberWorkoutProgressService.GetProgressByUserId(userId);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }
    }
}
