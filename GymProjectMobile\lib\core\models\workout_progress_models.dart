/// Workout Progress Models - GymKod Pro Mobile
///
/// Bu dosya antrenman ilerleme takibi için model sınıflarını içerir.
/// Backend DTO'larına karşılık gelir.
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_progress_models.g.dart';

/// Günlük ilerleme durumu modeli
/// Backend: DayProgressStatusDto
@JsonSerializable()
class DayProgressStatusModel {
  final int workoutProgramDayID;
  final int dayNumber;
  final String dayName;
  final bool isRestDay;
  final bool isCompleted;
  final DateTime? completedDate;

  const DayProgressStatusModel({
    required this.workoutProgramDayID,
    required this.dayNumber,
    required this.dayName,
    required this.isRestDay,
    required this.isCompleted,
    this.completedDate,
  });

  /// JSON'dan model oluştur
  factory DayProgressStatusModel.fromJson(Map<String, dynamic> json) =>
      _$DayProgressStatusModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$DayProgressStatusModelToJson(this);

  @override
  String toString() {
    return 'DayProgressStatusModel(dayNumber: $dayNumber, dayName: $dayName, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DayProgressStatusModel &&
        other.workoutProgramDayID == workoutProgramDayID &&
        other.dayNumber == dayNumber &&
        other.dayName == dayName &&
        other.isRestDay == isRestDay &&
        other.isCompleted == isCompleted &&
        other.completedDate == completedDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      workoutProgramDayID,
      dayNumber,
      dayName,
      isRestDay,
      isCompleted,
      completedDate,
    );
  }

  /// Copy with method
  DayProgressStatusModel copyWith({
    int? workoutProgramDayID,
    int? dayNumber,
    String? dayName,
    bool? isRestDay,
    bool? isCompleted,
    DateTime? completedDate,
  }) {
    return DayProgressStatusModel(
      workoutProgramDayID: workoutProgramDayID ?? this.workoutProgramDayID,
      dayNumber: dayNumber ?? this.dayNumber,
      dayName: dayName ?? this.dayName,
      isRestDay: isRestDay ?? this.isRestDay,
      isCompleted: isCompleted ?? this.isCompleted,
      completedDate: completedDate ?? this.completedDate,
    );
  }
}

/// Program ilerleme özeti modeli
/// Backend: WorkoutProgressSummaryDto
@JsonSerializable()
class WorkoutProgressSummaryModel {
  final int memberWorkoutProgramID;
  final int totalDays;
  final int completedDays;
  final int currentCycle;
  final double completionPercentage;
  final DateTime? lastCompletedDate;
  final List<int> completedDayIds;

  const WorkoutProgressSummaryModel({
    required this.memberWorkoutProgramID,
    required this.totalDays,
    required this.completedDays,
    required this.currentCycle,
    required this.completionPercentage,
    this.lastCompletedDate,
    required this.completedDayIds,
  });

  /// JSON'dan model oluştur
  factory WorkoutProgressSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgressSummaryModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$WorkoutProgressSummaryModelToJson(this);

  /// Tüm günler tamamlandı mı?
  bool get isAllDaysCompleted => totalDays > 0 && (completedDays % totalDays) == 0 && completedDays > 0;

  /// Mevcut döngüdeki ilerleme yüzdesi
  double get currentCycleProgress {
    if (totalDays == 0) return 0.0;
    final currentCycleCompletedDays = completedDays % totalDays;
    return (currentCycleCompletedDays / totalDays) * 100;
  }

  /// İlerleme var mı?
  bool get hasProgress => completedDays > 0;

  @override
  String toString() {
    return 'WorkoutProgressSummaryModel(programID: $memberWorkoutProgramID, cycle: $currentCycle, progress: ${completionPercentage.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkoutProgressSummaryModel &&
        other.memberWorkoutProgramID == memberWorkoutProgramID &&
        other.totalDays == totalDays &&
        other.completedDays == completedDays &&
        other.currentCycle == currentCycle &&
        other.completionPercentage == completionPercentage &&
        other.lastCompletedDate == lastCompletedDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      memberWorkoutProgramID,
      totalDays,
      completedDays,
      currentCycle,
      completionPercentage,
      lastCompletedDate,
    );
  }

  /// Copy with method
  WorkoutProgressSummaryModel copyWith({
    int? memberWorkoutProgramID,
    int? totalDays,
    int? completedDays,
    int? currentCycle,
    double? completionPercentage,
    DateTime? lastCompletedDate,
    List<int>? completedDayIds,
  }) {
    return WorkoutProgressSummaryModel(
      memberWorkoutProgramID: memberWorkoutProgramID ?? this.memberWorkoutProgramID,
      totalDays: totalDays ?? this.totalDays,
      completedDays: completedDays ?? this.completedDays,
      currentCycle: currentCycle ?? this.currentCycle,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      lastCompletedDate: lastCompletedDate ?? this.lastCompletedDate,
      completedDayIds: completedDayIds ?? this.completedDayIds,
    );
  }
}

/// Gün ilerleme toggle DTO'su
/// Backend: WorkoutProgressToggleDto
@JsonSerializable()
class WorkoutProgressToggleDto {
  final int memberWorkoutProgramID;
  final int workoutProgramDayID;

  const WorkoutProgressToggleDto({
    required this.memberWorkoutProgramID,
    required this.workoutProgramDayID,
  });

  /// JSON'dan model oluştur
  factory WorkoutProgressToggleDto.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgressToggleDtoFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$WorkoutProgressToggleDtoToJson(this);
}

/// Program ilerleme sıfırlama DTO'su
/// Backend: WorkoutProgressResetDto
@JsonSerializable()
class WorkoutProgressResetDto {
  final int memberWorkoutProgramID;

  const WorkoutProgressResetDto({
    required this.memberWorkoutProgramID,
  });

  /// JSON'dan model oluştur
  factory WorkoutProgressResetDto.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgressResetDtoFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$WorkoutProgressResetDtoToJson(this);
}
