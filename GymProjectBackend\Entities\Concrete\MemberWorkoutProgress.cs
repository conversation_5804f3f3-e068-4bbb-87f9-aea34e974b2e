using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    /// <summary>
    /// Üye antrenman programı günlük ilerleme takibi
    /// Her gün için tamamlanma durumu
    /// </summary>
    public class MemberWorkoutProgress : IEntity
    {
        [Key]
        public int MemberWorkoutProgressID { get; set; }
        
        /// <summary>
        /// Hangi üye program ataması
        /// </summary>
        public int MemberWorkoutProgramID { get; set; }
        
        /// <summary>
        /// Hangi program günü
        /// </summary>
        public int WorkoutProgramDayID { get; set; }
        
        /// <summary>
        /// Tamamlanma tarihi (sadece tarih, saat yok)
        /// </summary>
        public DateTime CompletedDate { get; set; }
        
        /// <summary>
        /// Tamamlandı mı? (şu an için her zaman true, gelecekte partial completion için)
        /// </summary>
        public bool IsCompleted { get; set; }
        
        /// <summary>
        /// Tam tamamlanma zamanı (tarih + saat)
        /// </summary>
        public DateTime CompletedAt { get; set; }
        
        /// <summary>
        /// Kayıt oluşturma tarihi
        /// </summary>
        public DateTime? CreationDate { get; set; }
        
        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime? UpdatedDate { get; set; }
    }
}
