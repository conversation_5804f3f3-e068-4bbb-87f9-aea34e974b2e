using Business.Abstract;
using Core.Aspects.Autofac.Caching;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    /// <summary>
    /// Üye antrenman ilerleme servisi
    /// </summary>
    public class MemberWorkoutProgressManager : IMemberWorkoutProgressService
    {
        private readonly IMemberWorkoutProgressDal _memberWorkoutProgressDal;
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly ICompanyContext _companyContext;

        public MemberWorkoutProgressManager(
            IMemberWorkoutProgressDal memberWorkoutProgressDal,
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            ICompanyContext companyContext)
        {
            _memberWorkoutProgressDal = memberWorkoutProgressDal;
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _companyContext = companyContext;
        }

        [SmartCacheRemoveAspect("WorkoutProgress")]
        public IResult ToggleDayProgress(WorkoutProgressToggleDto toggleDto, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişim yetkisi var mı kontrol et
                var hasAccess = CheckUserProgramAccess(toggleDto.MemberWorkoutProgramID, userId);
                if (!hasAccess.Success)
                {
                    return hasAccess;
                }

                var today = DateTime.Today;

                // Bu gün için zaten kayıt var mı kontrol et
                var existingProgress = _memberWorkoutProgressDal.GetProgressForDay(
                    toggleDto.MemberWorkoutProgramID, 
                    toggleDto.WorkoutProgramDayID, 
                    today);

                if (existingProgress != null)
                {
                    // Varsa sil (geri al)
                    _memberWorkoutProgressDal.Delete(new MemberWorkoutProgress 
                    { 
                        MemberWorkoutProgressID = existingProgress.MemberWorkoutProgressID 
                    });
                    
                    return new SuccessResult("Gün ilerlemesi geri alındı");
                }
                else
                {
                    // Yoksa ekle (tamamla)
                    var newProgress = new MemberWorkoutProgress
                    {
                        MemberWorkoutProgramID = toggleDto.MemberWorkoutProgramID,
                        WorkoutProgramDayID = toggleDto.WorkoutProgramDayID,
                        CompletedDate = today,
                        IsCompleted = true,
                        CompletedAt = DateTime.Now,
                        CreationDate = DateTime.Now
                    };

                    _memberWorkoutProgressDal.Add(newProgress);

                    // Otomatik döngü kontrolü
                    CheckAndStartNewCycle(toggleDto.MemberWorkoutProgramID, userId);

                    return new SuccessResult("Gün başarıyla tamamlandı");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İlerleme güncellenirken hata oluştu: {ex.Message}");
            }
        }

        [SmartCacheRemoveAspect("WorkoutProgress")]
        public IResult ResetProgramProgress(WorkoutProgressResetDto resetDto, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişim yetkisi var mı kontrol et
                var hasAccess = CheckUserProgramAccess(resetDto.MemberWorkoutProgramID, userId);
                if (!hasAccess.Success)
                {
                    return hasAccess;
                }

                // Tüm ilerleme kayıtlarını sil
                _memberWorkoutProgressDal.DeleteProgressByProgram(resetDto.MemberWorkoutProgramID);

                return new SuccessResult("Program ilerlemesi başarıyla sıfırlandı");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Program sıfırlanırken hata oluştu: {ex.Message}");
            }
        }

        [MultiTenantCacheAspect(duration: 30, "WorkoutProgress", "ByProgram")]
        public IDataResult<List<MemberWorkoutProgressDto>> GetProgressByProgram(int memberWorkoutProgramId, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişim yetkisi var mı kontrol et
                var hasAccess = CheckUserProgramAccess(memberWorkoutProgramId, userId);
                if (!hasAccess.Success)
                {
                    return new ErrorDataResult<List<MemberWorkoutProgressDto>>(hasAccess.Message);
                }

                var result = _memberWorkoutProgressDal.GetProgressByProgram(memberWorkoutProgramId);
                return new SuccessDataResult<List<MemberWorkoutProgressDto>>(result);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<MemberWorkoutProgressDto>>($"İlerleme getirilirken hata oluştu: {ex.Message}");
            }
        }

        [MultiTenantCacheAspect(duration: 30, "WorkoutProgress", "Summary")]
        public IDataResult<WorkoutProgressSummaryDto> GetProgressSummary(int memberWorkoutProgramId, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişim yetkisi var mı kontrol et
                var hasAccess = CheckUserProgramAccess(memberWorkoutProgramId, userId);
                if (!hasAccess.Success)
                {
                    return new ErrorDataResult<WorkoutProgressSummaryDto>(hasAccess.Message);
                }

                var result = _memberWorkoutProgressDal.GetProgressSummary(memberWorkoutProgramId);
                return new SuccessDataResult<WorkoutProgressSummaryDto>(result);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<WorkoutProgressSummaryDto>($"İlerleme özeti getirilirken hata oluştu: {ex.Message}");
            }
        }

        [MultiTenantCacheAspect(duration: 30, "WorkoutProgress", "DayStatus")]
        public IDataResult<List<DayProgressStatusDto>> GetDayProgressStatus(int memberWorkoutProgramId, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişim yetkisi var mı kontrol et
                var hasAccess = CheckUserProgramAccess(memberWorkoutProgramId, userId);
                if (!hasAccess.Success)
                {
                    return new ErrorDataResult<List<DayProgressStatusDto>>(hasAccess.Message);
                }

                var result = _memberWorkoutProgressDal.GetDayProgressStatus(memberWorkoutProgramId);
                return new SuccessDataResult<List<DayProgressStatusDto>>(result);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<DayProgressStatusDto>>($"Günlük ilerleme durumu getirilirken hata oluştu: {ex.Message}");
            }
        }

        [MultiTenantCacheAspect(duration: 30, "WorkoutProgress", "ByUser")]
        public IDataResult<List<WorkoutProgressSummaryDto>> GetProgressByUserId(int userId)
        {
            try
            {
                var result = _memberWorkoutProgressDal.GetProgressByUserId(userId);
                return new SuccessDataResult<List<WorkoutProgressSummaryDto>>(result);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<WorkoutProgressSummaryDto>>($"Kullanıcı ilerlemesi getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IResult CheckAndStartNewCycle(int memberWorkoutProgramId, int userId)
        {
            try
            {
                var summary = _memberWorkoutProgressDal.GetProgressSummary(memberWorkoutProgramId);
                
                // Tüm günler tamamlandı mı kontrol et
                if (summary.TotalDays > 0 && (summary.CompletedDays % summary.TotalDays) == 0 && summary.CompletedDays > 0)
                {
                    // Yeni döngü başladı mesajı (bu bilgi frontend'de gösterilecek)
                    return new SuccessResult($"Tebrikler! {summary.CurrentCycle}. döngüyü tamamladınız. Yeni döngü başlıyor!");
                }

                return new SuccessResult("Döngü kontrolü tamamlandı");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Döngü kontrolü yapılırken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// Kullanıcının programa erişim yetkisi var mı kontrol eder
        /// </summary>
        private IResult CheckUserProgramAccess(int memberWorkoutProgramId, int userId)
        {
            try
            {
                // Kullanıcının bu programa erişimi var mı kontrol et
                var userPrograms = _memberWorkoutProgramDal.GetActiveWorkoutProgramsByUserId(userId);
                var hasAccess = userPrograms.Any(p => p.MemberWorkoutProgramID == memberWorkoutProgramId);

                if (!hasAccess)
                {
                    return new ErrorResult("Bu programa erişim yetkiniz bulunmuyor");
                }

                return new SuccessResult();
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Yetki kontrolü yapılırken hata oluştu: {ex.Message}");
            }
        }
    }
}
