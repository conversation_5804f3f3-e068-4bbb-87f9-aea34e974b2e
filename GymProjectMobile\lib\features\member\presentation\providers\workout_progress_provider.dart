/// Workout Progress Provider - GymKod Pro Mobile
///
/// Bu provider antrenman ilerleme takibi state management'ı sağlar.
/// Günlük bazlı ilerleme takibi, otomatik döngü ve geri alma özellikleri
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../data/services/workout_program_api_service.dart';

/// Workout Progress State
class WorkoutProgressState {
  final Map<int, bool> dayProgress; // dayID -> isCompleted
  final WorkoutProgressSummaryModel? progressSummary;
  final List<DayProgressStatusModel> dayProgressStatus;
  final bool isLoading;
  final bool isToggling;
  final bool isResetting;
  final String? error;
  final String? successMessage;
  final DateTime? lastUpdated;
  final int? lastToggledDayId;

  const WorkoutProgressState({
    this.dayProgress = const {},
    this.progressSummary,
    this.dayProgressStatus = const [],
    this.isLoading = false,
    this.isToggling = false,
    this.isResetting = false,
    this.error,
    this.successMessage,
    this.lastUpdated,
    this.lastToggledDayId,
  });

  /// Copy with method
  WorkoutProgressState copyWith({
    Map<int, bool>? dayProgress,
    WorkoutProgressSummaryModel? progressSummary,
    List<DayProgressStatusModel>? dayProgressStatus,
    bool? isLoading,
    bool? isToggling,
    bool? isResetting,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    int? lastToggledDayId,
    bool clearError = false,
    bool clearSuccessMessage = false,
  }) {
    return WorkoutProgressState(
      dayProgress: dayProgress ?? this.dayProgress,
      progressSummary: progressSummary ?? this.progressSummary,
      dayProgressStatus: dayProgressStatus ?? this.dayProgressStatus,
      isLoading: isLoading ?? this.isLoading,
      isToggling: isToggling ?? this.isToggling,
      isResetting: isResetting ?? this.isResetting,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
      lastToggledDayId: lastToggledDayId ?? this.lastToggledDayId,
    );
  }

  /// İlerleme var mı?
  bool get hasProgress => dayProgress.isNotEmpty && dayProgress.values.any((completed) => completed);

  /// Belirli günün tamamlanma durumu
  bool isDayCompleted(int dayId) => dayProgress[dayId] ?? false;

  /// Tamamlanan gün sayısı
  int get completedDaysCount => dayProgress.values.where((completed) => completed).length;

  /// Toplam gün sayısı (dinlenme günleri hariç)
  int get totalActiveDaysCount => dayProgressStatus.where((day) => !day.isRestDay).length;

  /// İlerleme yüzdesi
  double get progressPercentage {
    if (totalActiveDaysCount == 0) return 0.0;
    return (completedDaysCount / totalActiveDaysCount) * 100;
  }

  @override
  String toString() {
    return 'WorkoutProgressState(hasProgress: $hasProgress, completedDays: $completedDaysCount, isLoading: $isLoading)';
  }
}

/// Workout Progress Notifier
class WorkoutProgressNotifier extends StateNotifier<WorkoutProgressState> {
  final WorkoutProgramRepository _repository;

  WorkoutProgressNotifier(this._repository) : super(const WorkoutProgressState());

  /// Program için ilerleme durumunu yükle
  Future<void> loadProgressStatus(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgress', 'Loading progress status', state: 'Program: $memberWorkoutProgramId');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      // Günlük ilerleme durumunu al
      final dayProgressResult = await _repository.getDayProgressStatus(memberWorkoutProgramId);
      
      if (dayProgressResult.isSuccess && dayProgressResult.data != null) {
        // Map'e çevir (dayId -> isCompleted)
        final progressMap = <int, bool>{};
        for (final dayStatus in dayProgressResult.data!) {
          progressMap[dayStatus.workoutProgramDayID] = dayStatus.isCompleted;
        }

        // İlerleme özetini al
        final summaryResult = await _repository.getProgressSummary(memberWorkoutProgramId);

        LoggingService.stateLog(
          'WorkoutProgress',
          'Progress status loaded successfully',
          state: 'Completed: ${progressMap.values.where((c) => c).length}/${progressMap.length}',
        );

        state = state.copyWith(
          dayProgress: progressMap,
          dayProgressStatus: dayProgressResult.data!,
          progressSummary: summaryResult.data,
          isLoading: false,
          lastUpdated: DateTime.now(),
          successMessage: 'İlerleme durumu yüklendi',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgress',
          'Failed to load progress status',
          state: dayProgressResult.message,
        );

        state = state.copyWith(
          isLoading: false,
          error: dayProgressResult.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.loadProgressStatus',
      );

      state = state.copyWith(
        isLoading: false,
        error: 'İlerleme durumu yüklenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Gün ilerlemesini toggle et (tamamla/geri al)
  Future<void> toggleDayProgress(int memberWorkoutProgramId, int workoutProgramDayId) async {
    try {
      LoggingService.stateLog(
        'WorkoutProgress',
        'Toggling day progress',
        state: 'Program: $memberWorkoutProgramId, Day: $workoutProgramDayId',
      );

      state = state.copyWith(
        isToggling: true,
        lastToggledDayId: workoutProgramDayId,
        clearError: true,
        clearSuccessMessage: true,
      );

      // Optimistic update
      final currentProgress = Map<int, bool>.from(state.dayProgress);
      currentProgress[workoutProgramDayId] = !(currentProgress[workoutProgramDayId] ?? false);
      
      state = state.copyWith(dayProgress: currentProgress);

      // API çağrısı
      final result = await _repository.toggleDayProgress(memberWorkoutProgramId, workoutProgramDayId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgress', 'Day progress toggled successfully');

        // İlerleme özetini güncelle
        final summaryResult = await _repository.getProgressSummary(memberWorkoutProgramId);

        state = state.copyWith(
          isToggling: false,
          progressSummary: summaryResult.data ?? state.progressSummary,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );

        // Otomatik döngü kontrolü
        _checkForCycleCompletion();
      } else {
        LoggingService.stateLog('WorkoutProgress', 'Failed to toggle day progress', state: result.message);

        // Optimistic update'i geri al
        final revertedProgress = Map<int, bool>.from(state.dayProgress);
        revertedProgress[workoutProgramDayId] = !(revertedProgress[workoutProgramDayId] ?? false);

        state = state.copyWith(
          dayProgress: revertedProgress,
          isToggling: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.toggleDayProgress',
      );

      // Optimistic update'i geri al
      final revertedProgress = Map<int, bool>.from(state.dayProgress);
      revertedProgress[workoutProgramDayId] = !(revertedProgress[workoutProgramDayId] ?? false);

      state = state.copyWith(
        dayProgress: revertedProgress,
        isToggling: false,
        error: 'Gün ilerlemesi güncellenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Program ilerlemesini sıfırla (tüm tikler kaldır)
  Future<void> resetProgramProgress(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgress', 'Resetting program progress', state: 'Program: $memberWorkoutProgramId');

      state = state.copyWith(
        isResetting: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      // API çağrısı
      final result = await _repository.resetProgramProgress(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgress', 'Program progress reset successfully');

        // Tüm günleri tamamlanmamış olarak işaretle
        final resetProgress = <int, bool>{};
        for (final dayId in state.dayProgress.keys) {
          resetProgress[dayId] = false;
        }

        // İlerleme özetini güncelle
        final summaryResult = await _repository.getProgressSummary(memberWorkoutProgramId);

        state = state.copyWith(
          dayProgress: resetProgress,
          progressSummary: summaryResult.data ?? state.progressSummary,
          isResetting: false,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog('WorkoutProgress', 'Failed to reset program progress', state: result.message);

        state = state.copyWith(
          isResetting: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.resetProgramProgress',
      );

      state = state.copyWith(
        isResetting: false,
        error: 'Program sıfırlanırken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Otomatik döngü tamamlanma kontrolü
  void _checkForCycleCompletion() {
    final summary = state.progressSummary;
    if (summary != null && summary.isAllDaysCompleted) {
      LoggingService.stateLog(
        'WorkoutProgress',
        'Cycle completed!',
        state: 'Cycle: ${summary.currentCycle}',
      );

      state = state.copyWith(
        successMessage: '🎉 ${summary.currentCycle}. döngüyü tamamladınız! Yeni döngü başlıyor...',
      );
    }
  }

  /// Cache'i kontrol et ve gerekirse yenile
  Future<void> loadIfNeeded(int memberWorkoutProgramId) async {
    // Eğer veri yoksa veya 5 dakikadan eski ise yenile
    final shouldLoad = state.dayProgress.isEmpty ||
        state.lastUpdated == null ||
        DateTime.now().difference(state.lastUpdated!).inMinutes > 5;

    if (shouldLoad) {
      await loadProgressStatus(memberWorkoutProgramId);
    }
  }

  /// State'i temizle
  void clearState() {
    state = const WorkoutProgressState();
  }
}

/// Workout Progress Provider
final workoutProgressProvider = StateNotifierProvider<WorkoutProgressNotifier, WorkoutProgressState>((ref) {
  final repository = ref.read(workoutProgramRepositoryProvider);
  return WorkoutProgressNotifier(repository);
});

/// Computed providers
final hasWorkoutProgressProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgressProvider).hasProgress;
});

final workoutProgressStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final state = ref.watch(workoutProgressProvider);
  return {
    'completedDays': state.completedDaysCount,
    'totalDays': state.totalActiveDaysCount,
    'progressPercentage': state.progressPercentage,
    'currentCycle': state.progressSummary?.currentCycle ?? 1,
  };
});

/// Belirli gün için tamamlanma durumu provider'ı
final dayCompletionProvider = Provider.family<bool, int>((ref, dayId) {
  return ref.watch(workoutProgressProvider).isDayCompleted(dayId);
});
