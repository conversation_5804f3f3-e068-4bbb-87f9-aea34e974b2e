-- =============================================
-- <PERSON><PERSON> Antrenman İlerleme Takip Sistemi Migration
-- Tarih: 2024
-- Açıklama: Günlük bazlı antrenman ilerleme takibi
-- =============================================

USE [GymProject]
GO

PRINT 'Üye Antrenman İlerleme Takip Sistemi migration başlıyor...'
GO

-- 1. MemberWorkoutProgress Tablosu (Günlük İlerleme Takibi)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MemberWorkoutProgress](
        [MemberWorkoutProgressID] [int] IDENTITY(1,1) NOT NULL,
        [MemberWorkoutProgramID] [int] NOT NULL,
        [WorkoutProgramDayID] [int] NOT NULL,
        [CompletedDate] [date] NOT NULL,
        [IsCompleted] [bit] NOT NULL DEFAULT(1),
        [CompletedAt] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
        [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
        [UpdatedDate] [datetime2](7) NULL,
        CONSTRAINT [PK_MemberWorkoutProgress] PRIMARY KEY CLUSTERED ([MemberWorkoutProgressID] ASC)
    ) ON [PRIMARY]
    
    PRINT 'MemberWorkoutProgress tablosu oluşturuldu.'
END
ELSE
BEGIN
    PRINT 'MemberWorkoutProgress tablosu zaten mevcut.'
END
GO

-- 2. Foreign Key Constraints
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_MemberWorkoutProgress_MemberWorkoutProgram]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutProgress] WITH CHECK 
    ADD CONSTRAINT [FK_MemberWorkoutProgress_MemberWorkoutProgram] 
    FOREIGN KEY([MemberWorkoutProgramID]) REFERENCES [dbo].[MemberWorkoutPrograms] ([MemberWorkoutProgramID])
    
    ALTER TABLE [dbo].[MemberWorkoutProgress] CHECK CONSTRAINT [FK_MemberWorkoutProgress_MemberWorkoutProgram]
    
    PRINT 'MemberWorkoutProgram foreign key eklendi.'
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_MemberWorkoutProgress_WorkoutProgramDay]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutProgress] WITH CHECK 
    ADD CONSTRAINT [FK_MemberWorkoutProgress_WorkoutProgramDay] 
    FOREIGN KEY([WorkoutProgramDayID]) REFERENCES [dbo].[WorkoutProgramDays] ([WorkoutProgramDayID])
    
    ALTER TABLE [dbo].[MemberWorkoutProgress] CHECK CONSTRAINT [FK_MemberWorkoutProgress_WorkoutProgramDay]
    
    PRINT 'WorkoutProgramDay foreign key eklendi.'
END
GO

-- 3. Performance Indexes (10.000+ kullanıcı için optimize)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]') AND name = N'IX_MemberWorkoutProgress_MemberProgram_Date')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgress_MemberProgram_Date] 
    ON [dbo].[MemberWorkoutProgress] ([MemberWorkoutProgramID], [CompletedDate])
    INCLUDE ([WorkoutProgramDayID], [IsCompleted], [CompletedAt])
    
    PRINT 'Performance index (MemberProgram_Date) eklendi.'
END
GO

-- 4. Unique Constraint (Bir gün sadece bir kez tamamlanabilir)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]') AND name = N'IX_MemberWorkoutProgress_Unique')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_MemberWorkoutProgress_Unique] 
    ON [dbo].[MemberWorkoutProgress] ([MemberWorkoutProgramID], [WorkoutProgramDayID], [CompletedDate])
    
    PRINT 'Unique constraint index eklendi.'
END
GO

-- 5. Additional Performance Index (User bazlı sorgular için)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]') AND name = N'IX_MemberWorkoutProgress_Day_Date')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgress_Day_Date] 
    ON [dbo].[MemberWorkoutProgress] ([WorkoutProgramDayID], [CompletedDate])
    INCLUDE ([MemberWorkoutProgramID], [IsCompleted])
    
    PRINT 'Additional performance index (Day_Date) eklendi.'
END
GO

-- 6. Check Constraints
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_MemberWorkoutProgress_CompletedDate]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutProgress]
    ADD CONSTRAINT [CK_MemberWorkoutProgress_CompletedDate] 
    CHECK ([CompletedDate] <= CAST(GETDATE() AS DATE))
    
    PRINT 'CompletedDate check constraint eklendi.'
END
GO

-- 7. Cache Configuration için Entity Settings (Performans optimizasyonu)
PRINT 'Cache ayarları:'
PRINT '- WorkoutProgress entity cache süresi: 30 dakika'
PRINT '- Cache tags: WorkoutProgress, ByProgram, Summary, DayStatus, ByUser'
PRINT '- Multi-tenant cache desteği aktif'
GO

-- 8. Test Data (Opsiyonel - Development için)
-- Bu kısım production'da çalıştırılmamalı
/*
IF EXISTS (SELECT * FROM sys.databases WHERE name = 'GymProject' AND collation_name LIKE '%Development%')
BEGIN
    PRINT 'Development ortamı tespit edildi, test verisi ekleniyor...'
    
    -- Test verisi burada eklenebilir
    
    PRINT 'Test verisi eklendi.'
END
*/

PRINT 'Üye Antrenman İlerleme Takip Sistemi migration tamamlandı!'
PRINT 'Oluşturulan tablolar:'
PRINT '- MemberWorkoutProgress (Günlük ilerleme takibi)'
PRINT 'Performans optimizasyonları:'
PRINT '- Composite index (MemberWorkoutProgramID, CompletedDate)'
PRINT '- Unique constraint (Duplicate prevention)'
PRINT '- Additional indexes (Query optimization)'
PRINT 'Cache sistemi hazır (30 dakika cache süresi)'
PRINT 'Mobil API için optimize edildi.'
GO
