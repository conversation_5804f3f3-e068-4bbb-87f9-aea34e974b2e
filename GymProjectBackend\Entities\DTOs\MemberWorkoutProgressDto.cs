using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// <PERSON><PERSON> antrenman ilerleme DTO'su
    /// </summary>
    public class MemberWorkoutProgressDto : IDto
    {
        public int MemberWorkoutProgressID { get; set; }
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public DateTime CompletedDate { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime CompletedAt { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    /// <summary>
    /// Gün ilerleme toggle DTO'su (mobil API için)
    /// </summary>
    public class WorkoutProgressToggleDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramDayID { get; set; }
    }

    /// <summary>
    /// Program ilerleme sıfırlama DTO'su
    /// </summary>
    public class WorkoutProgressResetDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
    }

    /// <summary>
    /// Program ilerleme özeti DTO'su
    /// </summary>
    public class WorkoutProgressSummaryDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int TotalDays { get; set; }
        public int CompletedDays { get; set; }
        public int CurrentCycle { get; set; }
        public double CompletionPercentage { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public List<int> CompletedDayIds { get; set; } = new List<int>();
    }

    /// <summary>
    /// Mobil API için günlük ilerleme durumu
    /// </summary>
    public class DayProgressStatusDto : IDto
    {
        public int WorkoutProgramDayID { get; set; }
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? CompletedDate { get; set; }
    }
}
