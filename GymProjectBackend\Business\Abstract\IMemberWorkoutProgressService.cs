using Core.Utilities.Results;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Abstract
{
    /// <summary>
    /// <PERSON>ye antrenman ilerleme servisi interface'i
    /// </summary>
    public interface IMemberWorkoutProgressService
    {
        /// <summary>
        /// Gün ilerleme durumunu toggle eder (tamamla/geri al)
        /// </summary>
        IResult ToggleDayProgress(WorkoutProgressToggleDto toggleDto, int userId);

        /// <summary>
        /// Program ilerlemesini sıfırlar (tüm tikler kaldırılır)
        /// </summary>
        IResult ResetProgramProgress(WorkoutProgressResetDto resetDto, int userId);

        /// <summary>
        /// Belirli program için ilerleme durumunu getirir
        /// </summary>
        IDataResult<List<MemberWorkoutProgressDto>> GetProgressByProgram(int memberWorkoutProgramId, int userId);

        /// <summary>
        /// Belirli program için ilerleme özetini getirir
        /// </summary>
        IDataResult<WorkoutProgressSummaryDto> GetProgressSummary(int memberWorkoutProgramId, int userId);

        /// <summary>
        /// Belirli program için günlük ilerleme durumlarını getirir
        /// </summary>
        IDataResult<List<DayProgressStatusDto>> GetDayProgressStatus(int memberWorkoutProgramId, int userId);

        /// <summary>
        /// User ID'ye göre aktif programların ilerleme durumunu getirir (mobil API için)
        /// </summary>
        IDataResult<List<WorkoutProgressSummaryDto>> GetProgressByUserId(int userId);

        /// <summary>
        /// Otomatik döngü kontrolü - tüm günler tamamlandıysa yeni döngü başlatır
        /// </summary>
        IResult CheckAndStartNewCycle(int memberWorkoutProgramId, int userId);
    }
}
