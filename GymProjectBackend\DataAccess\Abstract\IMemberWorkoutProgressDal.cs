using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Üye antrenman ilerleme veri erişim katmanı interface'i
    /// </summary>
    public interface IMemberWorkoutProgressDal : IEntityRepository<MemberWorkoutProgress>
    {
        /// <summary>
        /// Belirli program için ilerleme durumunu getirir
        /// </summary>
        List<MemberWorkoutProgressDto> GetProgressByProgram(int memberWorkoutProgramId);

        /// <summary>
        /// Belirli program için ilerleme özetini getirir
        /// </summary>
        WorkoutProgressSummaryDto GetProgressSummary(int memberWorkoutProgramId);

        /// <summary>
        /// Belirli program için günlük ilerleme durumlarını getirir
        /// </summary>
        List<DayProgressStatusDto> GetDayProgressStatus(int memberWorkoutProgramId);

        /// <summary>
        /// Belirli gün için ilerleme kaydı var mı kontrol eder
        /// </summary>
        bool HasProgressForDay(int memberWorkoutProgramId, int workoutProgramDayId, DateTime date);

        /// <summary>
        /// Belirli program için tüm ilerleme kayıtlarını siler
        /// </summary>
        void DeleteProgressByProgram(int memberWorkoutProgramId);

        /// <summary>
        /// User ID'ye göre aktif programların ilerleme durumunu getirir (mobil API için)
        /// </summary>
        List<WorkoutProgressSummaryDto> GetProgressByUserId(int userId);

        /// <summary>
        /// Belirli gün için ilerleme kaydını getirir
        /// </summary>
        MemberWorkoutProgressDto GetProgressForDay(int memberWorkoutProgramId, int workoutProgramDayId, DateTime date);
    }
}
