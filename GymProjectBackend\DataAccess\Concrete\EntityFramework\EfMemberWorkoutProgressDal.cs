using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// <PERSON>ye antrenman ilerleme veri er<PERSON><PERSON>
    /// </summary>
    public class EfMemberWorkoutProgressDal : EfEntityRepositoryBase<MemberWorkoutProgress, GymContext>, IMemberWorkoutProgressDal
    {
        public List<MemberWorkoutProgressDto> GetProgressByProgram(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutProgresses
                             where mwp.MemberWorkoutProgramID == memberWorkoutProgramId
                             orderby mwp.CompletedDate descending
                             select new MemberWorkoutProgressDto
                             {
                                 MemberWorkoutProgressID = mwp.MemberWorkoutProgressID,
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 WorkoutProgramDayID = mwp.WorkoutProgramDayID,
                                 CompletedDate = mwp.CompletedDate,
                                 IsCompleted = mwp.IsCompleted,
                                 CompletedAt = mwp.CompletedAt,
                                 CreationDate = mwp.CreationDate
                             };

                return result.ToList();
            }
        }

        public WorkoutProgressSummaryDto GetProgressSummary(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                // Program bilgilerini al
                var programInfo = (from mwp in context.MemberWorkoutPrograms
                                   join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                   where mwp.MemberWorkoutProgramID == memberWorkoutProgramId
                                   select new
                                   {
                                       MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                       TotalDays = context.WorkoutProgramDays
                                                   .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID && !d.IsRestDay)
                                                   .Count()
                                   }).FirstOrDefault();

                if (programInfo == null)
                {
                    return new WorkoutProgressSummaryDto
                    {
                        MemberWorkoutProgramID = memberWorkoutProgramId,
                        TotalDays = 0,
                        CompletedDays = 0,
                        CurrentCycle = 1,
                        CompletionPercentage = 0
                    };
                }

                // Tamamlanan günleri al
                var completedProgress = context.MemberWorkoutProgresses
                    .Where(p => p.MemberWorkoutProgramID == memberWorkoutProgramId)
                    .ToList();

                var completedDayIds = completedProgress.Select(p => p.WorkoutProgramDayID).Distinct().ToList();
                var completedDays = completedDayIds.Count;

                // Döngü hesaplama
                var currentCycle = completedDays > 0 ? (completedDays / programInfo.TotalDays) + 1 : 1;
                
                // Yüzde hesaplama (mevcut döngü için)
                var currentCycleProgress = completedDays % programInfo.TotalDays;
                var completionPercentage = programInfo.TotalDays > 0 ? 
                    (double)currentCycleProgress / programInfo.TotalDays * 100 : 0;

                // Son tamamlama tarihi
                var lastCompletedDate = completedProgress.Any() ? 
                    completedProgress.Max(p => p.CompletedDate) : (DateTime?)null;

                return new WorkoutProgressSummaryDto
                {
                    MemberWorkoutProgramID = memberWorkoutProgramId,
                    TotalDays = programInfo.TotalDays,
                    CompletedDays = completedDays,
                    CurrentCycle = currentCycle,
                    CompletionPercentage = completionPercentage,
                    LastCompletedDate = lastCompletedDate,
                    CompletedDayIds = completedDayIds
                };
            }
        }

        public List<DayProgressStatusDto> GetDayProgressStatus(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                // Program günlerini al
                var programDays = from mwp in context.MemberWorkoutPrograms
                                  join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                  join wpd in context.WorkoutProgramDays on wpt.WorkoutProgramTemplateID equals wpd.WorkoutProgramTemplateID
                                  where mwp.MemberWorkoutProgramID == memberWorkoutProgramId
                                  orderby wpd.DayNumber
                                  select new
                                  {
                                      WorkoutProgramDayID = wpd.WorkoutProgramDayID,
                                      DayNumber = wpd.DayNumber,
                                      DayName = wpd.DayName,
                                      IsRestDay = wpd.IsRestDay
                                  };

                // İlerleme kayıtlarını al
                var progressRecords = context.MemberWorkoutProgresses
                    .Where(p => p.MemberWorkoutProgramID == memberWorkoutProgramId)
                    .GroupBy(p => p.WorkoutProgramDayID)
                    .ToDictionary(g => g.Key, g => g.OrderByDescending(p => p.CompletedDate).First());

                var result = new List<DayProgressStatusDto>();

                foreach (var day in programDays)
                {
                    var hasProgress = progressRecords.ContainsKey(day.WorkoutProgramDayID);
                    
                    result.Add(new DayProgressStatusDto
                    {
                        WorkoutProgramDayID = day.WorkoutProgramDayID,
                        DayNumber = day.DayNumber,
                        DayName = day.DayName,
                        IsRestDay = day.IsRestDay,
                        IsCompleted = hasProgress,
                        CompletedDate = hasProgress ? progressRecords[day.WorkoutProgramDayID].CompletedDate : null
                    });
                }

                return result;
            }
        }

        public bool HasProgressForDay(int memberWorkoutProgramId, int workoutProgramDayId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberWorkoutProgresses
                    .Any(p => p.MemberWorkoutProgramID == memberWorkoutProgramId &&
                             p.WorkoutProgramDayID == workoutProgramDayId &&
                             p.CompletedDate.Date == date.Date);
            }
        }

        public void DeleteProgressByProgram(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                var progressRecords = context.MemberWorkoutProgresses
                    .Where(p => p.MemberWorkoutProgramID == memberWorkoutProgramId)
                    .ToList();

                context.MemberWorkoutProgresses.RemoveRange(progressRecords);
                context.SaveChanges();
            }
        }

        public List<WorkoutProgressSummaryDto> GetProgressByUserId(int userId)
        {
            using (GymContext context = new GymContext())
            {
                var userPrograms = from mwp in context.MemberWorkoutPrograms
                                   join m in context.Members on mwp.MemberID equals m.MemberID
                                   where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                                   select mwp.MemberWorkoutProgramID;

                var result = new List<WorkoutProgressSummaryDto>();

                foreach (var programId in userPrograms)
                {
                    var summary = GetProgressSummary(programId);
                    result.Add(summary);
                }

                return result;
            }
        }

        public MemberWorkoutProgressDto GetProgressForDay(int memberWorkoutProgramId, int workoutProgramDayId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutProgresses
                             where mwp.MemberWorkoutProgramID == memberWorkoutProgramId &&
                                   mwp.WorkoutProgramDayID == workoutProgramDayId &&
                                   mwp.CompletedDate.Date == date.Date
                             select new MemberWorkoutProgressDto
                             {
                                 MemberWorkoutProgressID = mwp.MemberWorkoutProgressID,
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 WorkoutProgramDayID = mwp.WorkoutProgramDayID,
                                 CompletedDate = mwp.CompletedDate,
                                 IsCompleted = mwp.IsCompleted,
                                 CompletedAt = mwp.CompletedAt,
                                 CreationDate = mwp.CreationDate
                             };

                return result.FirstOrDefault();
            }
        }
    }
}
