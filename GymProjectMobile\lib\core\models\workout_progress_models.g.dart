// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_progress_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DayProgressStatusModel _$DayProgressStatusModelFromJson(
  Map<String, dynamic> json,
) => DayProgressStatusModel(
  workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
  dayNumber: (json['dayNumber'] as num).toInt(),
  dayName: json['dayName'] as String,
  isRestDay: json['isRestDay'] as bool,
  isCompleted: json['isCompleted'] as bool,
  completedDate:
      json['completedDate'] == null
          ? null
          : DateTime.parse(json['completedDate'] as String),
);

Map<String, dynamic> _$DayProgressStatusModelToJson(
  DayProgressStatusModel instance,
) => <String, dynamic>{
  'workoutProgramDayID': instance.workoutProgramDayID,
  'dayNumber': instance.dayNumber,
  'dayName': instance.dayName,
  'isRestDay': instance.isRestDay,
  'isCompleted': instance.isCompleted,
  'completedDate': instance.completedDate?.toIso8601String(),
};

WorkoutProgressSummaryModel _$WorkoutProgressSummaryModelFromJson(
  Map<String, dynamic> json,
) => WorkoutProgressSummaryModel(
  memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
  totalDays: (json['totalDays'] as num).toInt(),
  completedDays: (json['completedDays'] as num).toInt(),
  currentCycle: (json['currentCycle'] as num).toInt(),
  completionPercentage: (json['completionPercentage'] as num).toDouble(),
  lastCompletedDate:
      json['lastCompletedDate'] == null
          ? null
          : DateTime.parse(json['lastCompletedDate'] as String),
  completedDayIds:
      (json['completedDayIds'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
);

Map<String, dynamic> _$WorkoutProgressSummaryModelToJson(
  WorkoutProgressSummaryModel instance,
) => <String, dynamic>{
  'memberWorkoutProgramID': instance.memberWorkoutProgramID,
  'totalDays': instance.totalDays,
  'completedDays': instance.completedDays,
  'currentCycle': instance.currentCycle,
  'completionPercentage': instance.completionPercentage,
  'lastCompletedDate': instance.lastCompletedDate?.toIso8601String(),
  'completedDayIds': instance.completedDayIds,
};

WorkoutProgressToggleDto _$WorkoutProgressToggleDtoFromJson(
  Map<String, dynamic> json,
) => WorkoutProgressToggleDto(
  memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
  workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
);

Map<String, dynamic> _$WorkoutProgressToggleDtoToJson(
  WorkoutProgressToggleDto instance,
) => <String, dynamic>{
  'memberWorkoutProgramID': instance.memberWorkoutProgramID,
  'workoutProgramDayID': instance.workoutProgramDayID,
};

WorkoutProgressResetDto _$WorkoutProgressResetDtoFromJson(
  Map<String, dynamic> json,
) => WorkoutProgressResetDto(
  memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
);

Map<String, dynamic> _$WorkoutProgressResetDtoToJson(
  WorkoutProgressResetDto instance,
) => <String, dynamic>{
  'memberWorkoutProgramID': instance.memberWorkoutProgramID,
};
