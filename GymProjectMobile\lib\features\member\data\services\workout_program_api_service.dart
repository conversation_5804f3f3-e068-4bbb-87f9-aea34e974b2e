/// Workout Program API Service - GymKod Pro Mobile
///
/// Bu service antrenman programı API'leri ile iletişim kurar.
/// Referans: Angular frontend'deki workout-program.service.ts
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';

/// Workout Program API Service Interface
abstract class WorkoutProgramApiService {
  /// Kullanıcının aktif antrenman programlarını al
  /// Backend: GetActiveWorkoutProgramsByUserId
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutProgramsByUserId();

  /// Program detayını al
  /// Backend: GetProgramDetailByUser
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetailByUser(int memberWorkoutProgramId);

  /// Gün ilerleme durumunu toggle et (tamamla/geri al)
  /// Backend: POST /api/MemberWorkoutProgress/toggle-day-progress
  Future<ApiResponse<void>> toggleDayProgress(int memberWorkoutProgramId, int workoutProgramDayId);

  /// Program ilerlemesini sıfırla (tüm tikler kaldır)
  /// Backend: POST /api/MemberWorkoutProgress/reset-program-progress
  Future<ApiResponse<void>> resetProgramProgress(int memberWorkoutProgramId);

  /// Program için günlük ilerleme durumlarını al
  /// Backend: GET /api/MemberWorkoutProgress/get-day-progress-status/{id}
  Future<ApiResponse<List<DayProgressStatusModel>>> getDayProgressStatus(int memberWorkoutProgramId);

  /// Program için ilerleme özetini al
  /// Backend: GET /api/MemberWorkoutProgress/get-progress-summary/{id}
  Future<ApiResponse<WorkoutProgressSummaryModel>> getProgressSummary(int memberWorkoutProgramId);
}

/// Workout Program API Service Implementation
class WorkoutProgramApiServiceImpl implements WorkoutProgramApiService {
  final ApiService _apiService;

  WorkoutProgramApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutProgramsByUserId() async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgram/getactiveprogramsbyuser');

      // JWT token'dan user ID'yi al
      final accessToken = await StorageService().getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found for workout programs');
        return ApiResponse.error(message: 'Oturum süresi dolmuş. Lütfen tekrar giriş yapın.');
      }

      final userModel = JwtService().decodeToken(accessToken);
      if (userModel == null) {
        LoggingService.authLog('Invalid token for workout programs');
        return ApiResponse.error(message: 'Geçersiz oturum. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.get(
        'MemberWorkoutProgram/getactiveprogramsbyuser',
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgram/getactiveprogramsbyuser',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          final dataList = responseData['data'] as List<dynamic>? ?? [];
          
          LoggingService.stateLog(
            'WorkoutProgram',
            'Active programs loaded successfully',
            state: 'Count: ${dataList.length}',
          );

          // DTO'ları model'lere çevir
          final programs = dataList
              .map((json) => MemberActiveWorkoutProgramModel.fromJson(json as Map<String, dynamic>))
              .toList();

          return ApiResponse.success(
            data: programs,
            message: responseData['message'] ?? 'Antrenman programları başarıyla yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] ?? 'Antrenman programları yüklenemedi';
          LoggingService.stateLog('WorkoutProgram', 'API returned error', state: errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.apiError(
          'GET',
          'MemberWorkoutProgram/getactiveprogramsbyuser',
          'Invalid response: ${response.statusCode}',
        );
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiService.getActiveWorkoutProgramsByUserId',
      );

      // Network hatası kontrolü
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        return ApiResponse.error(message: AppConstants.networkErrorMessage);
      }

      return ApiResponse.error(message: 'Antrenman programları yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetailByUser(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgram/getprogramdetailbyuser');

      // JWT token'dan user ID'yi al
      final accessToken = await StorageService().getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found for program detail');
        return ApiResponse.error(message: 'Oturum süresi dolmuş. Lütfen tekrar giriş yapın.');
      }

      final userModel = JwtService().decodeToken(accessToken);
      if (userModel == null) {
        LoggingService.authLog('Invalid token for program detail');
        return ApiResponse.error(message: 'Geçersiz oturum. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.get(
        'MemberWorkoutProgram/getprogramdetailbyuser',
        queryParameters: {
          'userId': userId,
          'memberWorkoutProgramId': memberWorkoutProgramId,
        },
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgram/getprogramdetailbyuser',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          final data = responseData['data'] as Map<String, dynamic>?;

          if (data != null) {
            LoggingService.stateLog(
              'WorkoutProgramDetail',
              'Program detail loaded successfully',
              state: 'Program: ${data['programName']}',
            );

            // DTO'yu model'e çevir
            final programDetail = MemberWorkoutProgramDetailModel.fromJson(data);

            return ApiResponse.success(
              data: programDetail,
              message: responseData['message'] ?? 'Program detayı başarıyla yüklendi',
            );
          } else {
            LoggingService.stateLog('WorkoutProgramDetail', 'No program data found');
            return ApiResponse.error(message: 'Program detayı bulunamadı');
          }
        } else {
          final errorMessage = responseData['message'] ?? 'Program detayı yüklenemedi';
          LoggingService.stateLog('WorkoutProgramDetail', 'API returned error', state: errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.apiError(
          'GET',
          'MemberWorkoutProgram/getprogramdetailbyuser',
          'Invalid response: ${response.statusCode}',
        );
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiService.getProgramDetailByUser',
      );

      // Network hatası kontrolü
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        return ApiResponse.error(message: AppConstants.networkErrorMessage);
      }

      return ApiResponse.error(message: 'Program detayı yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<void>> toggleDayProgress(int memberWorkoutProgramId, int workoutProgramDayId) async {
    try {
      LoggingService.apiRequest('POST', 'MemberWorkoutProgress/toggle-day-progress');

      // Request body
      final requestBody = WorkoutProgressToggleDto(
        memberWorkoutProgramID: memberWorkoutProgramId,
        workoutProgramDayID: workoutProgramDayId,
      ).toJson();

      // API çağrısı
      final response = await _apiService.post(
        'MemberWorkoutProgress/toggle-day-progress',
        data: requestBody,
      );

      LoggingService.apiResponse(
        'POST',
        'MemberWorkoutProgress/toggle-day-progress',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200) {
        LoggingService.info('Day progress toggled successfully');
        return ApiResponse.success(
          data: null,
          message: 'Gün ilerlemesi güncellendi',
        );
      } else {
        final errorMessage = response.data?['message'] ?? 'Gün ilerlemesi güncellenirken hata oluştu';
        LoggingService.error('Failed to toggle day progress: $errorMessage');
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiServiceImpl.toggleDayProgress',
      );

      return ApiResponse.error(message: 'Gün ilerlemesi güncellenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<void>> resetProgramProgress(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('POST', 'MemberWorkoutProgress/reset-program-progress');

      // Request body
      final requestBody = WorkoutProgressResetDto(
        memberWorkoutProgramID: memberWorkoutProgramId,
      ).toJson();

      // API çağrısı
      final response = await _apiService.post(
        'MemberWorkoutProgress/reset-program-progress',
        data: requestBody,
      );

      LoggingService.apiResponse(
        'POST',
        'MemberWorkoutProgress/reset-program-progress',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200) {
        LoggingService.info('Program progress reset successfully');
        return ApiResponse.success(
          data: null,
          message: 'Program ilerlemesi sıfırlandı',
        );
      } else {
        final errorMessage = response.data?['message'] ?? 'Program sıfırlanırken hata oluştu';
        LoggingService.error('Failed to reset program progress: $errorMessage');
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiServiceImpl.resetProgramProgress',
      );

      return ApiResponse.error(message: 'Program sıfırlanırken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<List<DayProgressStatusModel>>> getDayProgressStatus(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgress/get-day-progress-status/$memberWorkoutProgramId');

      // API çağrısı
      final response = await _apiService.get(
        'MemberWorkoutProgress/get-day-progress-status/$memberWorkoutProgramId',
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgress/get-day-progress-status/$memberWorkoutProgramId',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> dataList = responseData['data'] as List<dynamic>;

          final dayProgressList = dataList
              .map((item) => DayProgressStatusModel.fromJson(item as Map<String, dynamic>))
              .toList();

          LoggingService.info('Day progress status loaded successfully: ${dayProgressList.length} days');

          return ApiResponse.success(
            data: dayProgressList,
            message: responseData['message'] ?? 'Günlük ilerleme durumu yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] ?? 'Günlük ilerleme durumu yüklenirken hata oluştu';
          LoggingService.error('API returned error: $errorMessage');
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.error('Invalid response from API');
        return ApiResponse.error(message: 'Geçersiz API yanıtı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiServiceImpl.getDayProgressStatus',
      );

      return ApiResponse.error(message: 'Günlük ilerleme durumu yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<WorkoutProgressSummaryModel>> getProgressSummary(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgress/get-progress-summary/$memberWorkoutProgramId');

      // API çağrısı
      final response = await _apiService.get(
        'MemberWorkoutProgress/get-progress-summary/$memberWorkoutProgramId',
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgress/get-progress-summary/$memberWorkoutProgramId',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final progressSummary = WorkoutProgressSummaryModel.fromJson(
            responseData['data'] as Map<String, dynamic>
          );

          LoggingService.info('Progress summary loaded successfully: Cycle ${progressSummary.currentCycle}');

          return ApiResponse.success(
            data: progressSummary,
            message: responseData['message'] ?? 'İlerleme özeti yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] ?? 'İlerleme özeti yüklenirken hata oluştu';
          LoggingService.error('API returned error: $errorMessage');
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.error('Invalid response from API');
        return ApiResponse.error(message: 'Geçersiz API yanıtı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiServiceImpl.getProgressSummary',
      );

      return ApiResponse.error(message: 'İlerleme özeti yüklenirken hata oluştu: ${e.toString()}');
    }
  }
}

/// Workout Program Repository Interface
abstract class WorkoutProgramRepository {
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutPrograms();
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetail(int memberWorkoutProgramId);
  Future<ApiResponse<void>> toggleDayProgress(int memberWorkoutProgramId, int workoutProgramDayId);
  Future<ApiResponse<void>> resetProgramProgress(int memberWorkoutProgramId);
  Future<ApiResponse<List<DayProgressStatusModel>>> getDayProgressStatus(int memberWorkoutProgramId);
  Future<ApiResponse<WorkoutProgressSummaryModel>> getProgressSummary(int memberWorkoutProgramId);
}

/// Workout Program Repository Implementation
class WorkoutProgramRepositoryImpl implements WorkoutProgramRepository {
  final WorkoutProgramApiService _apiService;

  WorkoutProgramRepositoryImpl(this._apiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutPrograms() async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading active workout programs');
      
      final result = await _apiService.getActiveWorkoutProgramsByUserId();
      
      if (result.isSuccess) {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Active workout programs loaded',
          state: 'Count: ${result.data?.length ?? 0}',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Failed to load workout programs',
          state: result.message,
        );
      }
      
      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramRepository.getActiveWorkoutPrograms',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetail(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading program detail', state: 'ID: $memberWorkoutProgramId');

      final result = await _apiService.getProgramDetailByUser(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Program detail loaded',
          state: 'Program: ${result.data?.programName}',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Failed to load program detail',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramRepository.getProgramDetail',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<void>> toggleDayProgress(int memberWorkoutProgramId, int workoutProgramDayId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Toggling day progress',
        state: 'Program: $memberWorkoutProgramId, Day: $workoutProgramDayId');

      final result = await _apiService.toggleDayProgress(memberWorkoutProgramId, workoutProgramDayId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgramRepository', 'Day progress toggled successfully');
      } else {
        LoggingService.stateLog('WorkoutProgramRepository', 'Failed to toggle day progress', state: result.message);
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository.toggleDayProgress');
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<void>> resetProgramProgress(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Resetting program progress',
        state: 'Program: $memberWorkoutProgramId');

      final result = await _apiService.resetProgramProgress(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgramRepository', 'Program progress reset successfully');
      } else {
        LoggingService.stateLog('WorkoutProgramRepository', 'Failed to reset program progress', state: result.message);
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository.resetProgramProgress');
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<List<DayProgressStatusModel>>> getDayProgressStatus(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading day progress status',
        state: 'Program: $memberWorkoutProgramId');

      final result = await _apiService.getDayProgressStatus(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgramRepository', 'Day progress status loaded',
          state: 'Count: ${result.data?.length ?? 0}');
      } else {
        LoggingService.stateLog('WorkoutProgramRepository', 'Failed to load day progress status', state: result.message);
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository.getDayProgressStatus');
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<WorkoutProgressSummaryModel>> getProgressSummary(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading progress summary',
        state: 'Program: $memberWorkoutProgramId');

      final result = await _apiService.getProgressSummary(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog('WorkoutProgramRepository', 'Progress summary loaded',
          state: 'Cycle: ${result.data?.currentCycle}');
      } else {
        LoggingService.stateLog('WorkoutProgramRepository', 'Failed to load progress summary', state: result.message);
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository.getProgressSummary');
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }
}

/// Providers
final workoutProgramApiServiceProvider = Provider<WorkoutProgramApiService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return WorkoutProgramApiServiceImpl(apiService);
});

final workoutProgramRepositoryProvider = Provider<WorkoutProgramRepository>((ref) {
  final apiService = ref.read(workoutProgramApiServiceProvider);
  return WorkoutProgramRepositoryImpl(apiService);
});
