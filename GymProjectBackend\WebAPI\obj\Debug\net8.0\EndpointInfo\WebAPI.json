{"openapi": "3.0.1", "info": {"title": "WebAPI", "version": "1.0"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequestModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/register-member": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberRegisterRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberRegisterRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MemberRegisterRequestModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequestModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/revoke-device": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "deviceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/revoke-all-devices": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/devices": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/change-company": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeCompanyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeCompanyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeCompanyRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/check-password-change-required": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/check-login-ban": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "deviceInfo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/check-register-ban": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/statistics": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/health": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/keys": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/keys/pattern/{pattern}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "pattern", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/size": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/clear": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/clear/tenant/{tenantId}": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/clear/entity/{tenantId}/{entityName}": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "entityName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/test": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/statistics/detailed": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/statistics/tenant/{tenantId}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenants": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenants/sizes": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenant/{tenantId}/keys": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenant/{tenantId}/size": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenant/{tenantId}/entities": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenant/{tenantId}/details": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/tenant/{tenantId}/entity/{entityName}/keys": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "entityName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/item/{key}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CacheAdmin/performance": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/City/getall": {"get": {"tags": ["City"], "responses": {"200": {"description": "Success"}}}}, "/api/Company/getall": {"get": {"tags": ["Company"], "responses": {"200": {"description": "Success"}}}}, "/api/Company/add": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Company"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Company"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Company/delete": {"delete": {"tags": ["Company"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Company/update": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Company"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Company"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Company/getactivecompanies": {"get": {"tags": ["Company"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyAdress/getall": {"get": {"tags": ["CompanyAdress"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyAdress/add": {"post": {"tags": ["CompanyAdress"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyAdress/delete": {"delete": {"tags": ["CompanyAdress"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyAdress/update": {"post": {"tags": ["CompanyAdress"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyAdress"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyAdress/getcompanyadressdetails": {"get": {"tags": ["CompanyAdress"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getall": {"get": {"tags": ["CompanyExercises"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getbycategory/{categoryId}": {"get": {"tags": ["CompanyExercises"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getfiltered": {"post": {"tags": ["CompanyExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseFilterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseFilterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseFilterDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/search": {"get": {"tags": ["CompanyExercises"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getdetail/{id}": {"get": {"tags": ["CompanyExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getbyid/{id}": {"get": {"tags": ["CompanyExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/add": {"post": {"tags": ["CompanyExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/update": {"post": {"tags": ["CompanyExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyExerciseUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/delete/{id}": {"delete": {"tags": ["CompanyExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getcombined": {"get": {"tags": ["CompanyExercises"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getcombinedbycategory/{categoryId}": {"get": {"tags": ["CompanyExercises"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyExercises/getcombinedfiltered": {"post": {"tags": ["CompanyExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/getall": {"get": {"tags": ["CompanyUser"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/add": {"post": {"tags": ["CompanyUser"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/delete": {"delete": {"tags": ["CompanyUser"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/update": {"post": {"tags": ["CompanyUser"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompanyUser"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/getcompanydetails": {"get": {"tags": ["CompanyUser"], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/getbycityid": {"get": {"tags": ["CompanyUser"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/getcompanyuserdetailsbycityid": {"get": {"tags": ["CompanyUser"], "parameters": [{"name": "cityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/CompanyUser/getcompanyuserdetails": {"get": {"tags": ["CompanyUser"], "responses": {"200": {"description": "Success"}}}}, "/api/DebtPayments/delete": {"delete": {"tags": ["DebtPayments"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/getall": {"get": {"tags": ["ExerciseCategories"], "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/getactive": {"get": {"tags": ["ExerciseCategories"], "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/getbyid/{id}": {"get": {"tags": ["ExerciseCategories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/add": {"post": {"tags": ["ExerciseCategories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/update": {"post": {"tags": ["ExerciseCategories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExerciseCategoryUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ExerciseCategories/delete/{id}": {"delete": {"tags": ["ExerciseCategories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/getall": {"get": {"tags": ["Expenses"], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/getbyid": {"get": {"tags": ["Expenses"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/add": {"post": {"tags": ["Expenses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Expense"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Expense"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Expense"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/update": {"post": {"tags": ["Expenses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Expense"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Expense"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Expense"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/delete": {"delete": {"tags": ["Expenses"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/getbydaterange": {"get": {"tags": ["Expenses"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/getmonthlyexpenses": {"get": {"tags": ["Expenses"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/gettotalmonthlyexpenses": {"get": {"tags": ["Expenses"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/gettotaldailyexpenses": {"get": {"tags": ["Expenses"], "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/gettotalyearlyexpenses": {"get": {"tags": ["Expenses"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Expenses/getmonthlyexpensesummary": {"get": {"tags": ["Expenses"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/LicensePackages/getall": {"get": {"tags": ["LicensePackages"], "responses": {"200": {"description": "Success"}}}}, "/api/LicensePackages/getbyid": {"get": {"tags": ["LicensePackages"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/LicensePackages/add": {"post": {"tags": ["LicensePackages"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/LicensePackages/update": {"post": {"tags": ["LicensePackages"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LicensePackage"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/LicensePackages/delete": {"delete": {"tags": ["LicensePackages"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/LicenseTransactions/getall": {"get": {"tags": ["LicenseTransactions"], "responses": {"200": {"description": "Success"}}}}, "/api/LicenseTransactions/getbyuserid": {"get": {"tags": ["LicenseTransactions"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/LicenseTransactions/add": {"post": {"tags": ["LicenseTransactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LicenseTransaction"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LicenseTransaction"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LicenseTransaction"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Member/getall": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/add": {"post": {"tags": ["Member"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Member"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Member"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Member"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Member/delete": {"delete": {"tags": ["Member"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/update": {"post": {"tags": ["Member"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Member"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Member"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Member"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberdetails": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getactivemembers": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberentryexithistory": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberremainingday": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/scannumber": {"post": {"tags": ["Member"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanMemberForApiDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ScanMemberForApiDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ScanMemberForApiDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Member/getbymemberid": {"get": {"tags": ["Member"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getbyphone": {"get": {"tags": ["Member"], "parameters": [{"name": "phoneNumber", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/gettodayentries": {"get": {"tags": ["Member"], "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getallpaginated": {"get": {"tags": ["Member"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "searchText", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "gender", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberdetailspaginated": {"get": {"tags": ["Member"], "parameters": [{"name": "Gender", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Branch", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "SearchText", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/gettotalactivemembers": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/gettotalregisteredmembers": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getactivemembercounts": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getbranchcounts": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberentriesbysearch": {"get": {"tags": ["Member"], "parameters": [{"name": "searchText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberdetailbyid/{id}": {"get": {"tags": ["Member"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getupcomingbirthdays": {"get": {"tags": ["Member"], "parameters": [{"name": "days", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 3}}], "responses": {"200": {"description": "Success"}}}}, "/api/Member/getmemberqrbyuserid": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/get-profile": {"get": {"tags": ["Member"], "responses": {"200": {"description": "Success"}}}}, "/api/Member/update-profile": {"post": {"tags": ["Member"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberProfileUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberProfileUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MemberProfileUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Membership/cancel-freeze/{id}": {"post": {"tags": ["Membership"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/reactivate-from-today/{id}": {"post": {"tags": ["Membership"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/freeze": {"post": {"tags": ["Membership"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Membership/unfreeze/{id}": {"post": {"tags": ["Membership"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/frozen": {"get": {"tags": ["Membership"], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/getall": {"get": {"tags": ["Membership"], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/add": {"post": {"tags": ["Membership"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Membership/delete": {"delete": {"tags": ["Membership"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/update": {"post": {"tags": ["Membership"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Membership/getbymembershipid": {"get": {"tags": ["Membership"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Membership/getlastmembershipinfo/{memberId}": {"get": {"tags": ["Membership"], "parameters": [{"name": "memberId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipFreezeHistory/getall": {"get": {"tags": ["MembershipFreezeHistory"], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipFreezeHistory/getbymembershipid/{membershipId}": {"get": {"tags": ["MembershipFreezeHistory"], "parameters": [{"name": "membershipId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipFreezeHistory/getremainingfreezedays/{membershipId}": {"get": {"tags": ["MembershipFreezeHistory"], "parameters": [{"name": "membershipId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipFreezeHistory/add": {"post": {"tags": ["MembershipFreezeHistory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MembershipFreezeHistory/update": {"post": {"tags": ["MembershipFreezeHistory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipFreezeHistory"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MembershipType/getall": {"get": {"tags": ["MembershipType"], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipType/add": {"post": {"tags": ["MembershipType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MembershipType/delete": {"delete": {"tags": ["MembershipType"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MembershipType/update": {"post": {"tags": ["MembershipType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MembershipType"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MembershipType/getallbranches": {"get": {"tags": ["MembershipType"], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/assign": {"post": {"tags": ["MemberWorkoutProgram"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/update": {"put": {"tags": ["MemberWorkoutProgram"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MemberWorkoutProgramUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/delete": {"delete": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getcompanyassignments": {"get": {"tags": ["MemberWorkoutProgram"], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getmemberactiveprograms": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "memberId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getmemberprogramhistory": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "memberId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getactiveprogramsbyuser": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getassignmentdetail": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getassignedmembercount": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "workoutProgramTemplateId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getprogramdetailbyuser": {"get": {"tags": ["MemberWorkoutProgram"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "memberWorkoutProgramId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgram/getactiveassignmentcount": {"get": {"tags": ["MemberWorkoutProgram"], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/toggle-day-progress": {"post": {"tags": ["MemberWorkoutProgress"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressToggleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressToggleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressToggleDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/reset-program-progress": {"post": {"tags": ["MemberWorkoutProgress"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressResetDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressResetDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkoutProgressResetDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/get-progress-by-program/{memberWorkoutProgramId}": {"get": {"tags": ["MemberWorkoutProgress"], "parameters": [{"name": "memberWorkoutProgramId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/get-progress-summary/{memberWorkoutProgramId}": {"get": {"tags": ["MemberWorkoutProgress"], "parameters": [{"name": "memberWorkoutProgramId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/get-day-progress-status/{memberWorkoutProgramId}": {"get": {"tags": ["MemberWorkoutProgress"], "parameters": [{"name": "memberWorkoutProgramId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/MemberWorkoutProgress/get-progress-by-user": {"get": {"tags": ["MemberWorkoutProgress"], "responses": {"200": {"description": "Success"}}}}, "/api/OperationClaims/getall": {"get": {"tags": ["OperationClaims"], "responses": {"200": {"description": "Success"}}}}, "/api/OperationClaims/getbyid": {"get": {"tags": ["OperationClaims"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/OperationClaims/add": {"post": {"tags": ["OperationClaims"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/OperationClaims/update": {"post": {"tags": ["OperationClaims"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OperationClaim"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/OperationClaims/delete": {"delete": {"tags": ["OperationClaims"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getall": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/add": {"post": {"tags": ["Payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Payment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Payment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Payment"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Payment/delete": {"delete": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/update": {"post": {"tags": ["Payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Payment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Payment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Payment"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getpaymenthistory": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/GetDebtorMembers": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/updatestatus/{id}": {"post": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentUpdateModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentUpdateModelDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentUpdateModelDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getpaymenthistorypaginated": {"get": {"tags": ["Payment"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getpaymenttotals": {"get": {"tags": ["Payment"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getmonthlyrevenue": {"get": {"tags": ["Payment"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Success"}}}}, "/api/Payment/getallpaymenthistoryfiltered": {"get": {"tags": ["Payment"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Products/getall": {"get": {"tags": ["Products"], "responses": {"200": {"description": "Success"}}}}, "/api/Products/getbyid": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Products/add": {"post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Products/update": {"post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Products/delete": {"post": {"tags": ["Products"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/RemainingDebts/getremainingdebtdetails": {"get": {"tags": ["RemainingDebts"], "responses": {"200": {"description": "Success"}}}}, "/api/RemainingDebts/adddebtpayment": {"post": {"tags": ["RemainingDebts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DebtPaymentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DebtPaymentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DebtPaymentDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/getall": {"get": {"tags": ["SystemExercises"], "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/getbycategory/{categoryId}": {"get": {"tags": ["SystemExercises"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/getfiltered": {"post": {"tags": ["SystemExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SystemExerciseFilterDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/search": {"get": {"tags": ["SystemExercises"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/getdetail/{id}": {"get": {"tags": ["SystemExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/getbyid/{id}": {"get": {"tags": ["SystemExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/add": {"post": {"tags": ["SystemExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SystemExerciseAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/update": {"post": {"tags": ["SystemExercises"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SystemExerciseUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SystemExerciseUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SystemExercises/delete/{id}": {"delete": {"tags": ["SystemExercises"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Town/getall": {"get": {"tags": ["Town"], "responses": {"200": {"description": "Success"}}}}, "/api/Town/getbycityid": {"get": {"tags": ["Town"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/getall": {"get": {"tags": ["Transactions"], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/updateallpaymentstatus/{memberId}": {"post": {"tags": ["Transactions"], "parameters": [{"name": "memberId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/getbymemberid": {"get": {"tags": ["Transactions"], "parameters": [{"name": "memberId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/add": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Transaction"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Transaction"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Transaction"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/getwithuserproductdetails": {"get": {"tags": ["Transactions"], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/updatepaymentstatus/{id}": {"post": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/addBulk": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkTransactionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkTransactionDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Transactions/getunpaidtransactions": {"get": {"tags": ["Transactions"], "parameters": [{"name": "memberId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/User/getall": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/upload-profile-image": {"post": {"tags": ["User"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/User/delete-profile-image": {"delete": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/profile-image/{userId}": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/User/remaining-profile-image-uploads": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/remaining-file-downloads": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/record-file-download": {"post": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/User/health": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success"}}}}, "/api/UserCompany/getall": {"get": {"tags": ["UserCompany"], "responses": {"200": {"description": "Success"}}}}, "/api/UserCompany/add": {"post": {"tags": ["UserCompany"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserCompany/delete": {"delete": {"tags": ["UserCompany"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserCompany/update": {"post": {"tags": ["UserCompany"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCompany"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserCompany/getusercompanydetails": {"get": {"tags": ["UserCompany"], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/getall": {"get": {"tags": ["UserLicenses"], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/getbyid": {"get": {"tags": ["UserLicenses"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/getactivebyuserid": {"get": {"tags": ["UserLicenses"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/getuserroles": {"get": {"tags": ["UserLicenses"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/add": {"post": {"tags": ["UserLicenses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/update": {"post": {"tags": ["UserLicenses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLicense"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/delete": {"delete": {"tags": ["UserLicenses"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/purchase": {"post": {"tags": ["UserLicenses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LicensePurchaseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LicensePurchaseDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LicensePurchaseDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/extend": {"post": {"tags": ["UserLicenses"], "parameters": [{"name": "userLicenseId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "extensionDays", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserLicenses/revoke": {"post": {"tags": ["UserLicenses"], "parameters": [{"name": "userLicenseId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/getall": {"get": {"tags": ["UserOperationClaims"], "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/getbyid": {"get": {"tags": ["UserOperationClaims"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/add": {"post": {"tags": ["UserOperationClaims"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/update": {"post": {"tags": ["UserOperationClaims"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserOperationClaim"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/delete": {"delete": {"tags": ["UserOperationClaims"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/UserOperationClaims/getuseroperationclaimdetails": {"get": {"tags": ["UserOperationClaims"], "responses": {"200": {"description": "Success"}}}}, "/api/WorkoutProgram/getall": {"get": {"tags": ["WorkoutProgram"], "responses": {"200": {"description": "Success"}}}}, "/api/WorkoutProgram/getbyid": {"get": {"tags": ["WorkoutProgram"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/WorkoutProgram/add": {"post": {"tags": ["WorkoutProgram"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateAddDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateAddDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateAddDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/WorkoutProgram/update": {"put": {"tags": ["WorkoutProgram"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkoutProgramTemplateUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/WorkoutProgram/delete": {"delete": {"tags": ["WorkoutProgram"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"BulkTransactionDto": {"type": "object", "properties": {"memberID": {"type": "integer", "format": "int32"}, "transactionType": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionItemDto"}, "nullable": true}}, "additionalProperties": false}, "ChangeCompanyRequest": {"required": ["companyId"], "type": "object", "properties": {"companyId": {"type": "integer", "format": "int32"}, "deviceInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"required": ["currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "Company": {"type": "object", "properties": {"companyID": {"type": "integer", "format": "int32"}, "companyName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CompanyAdress": {"type": "object", "properties": {"companyAdressID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "cityID": {"type": "integer", "format": "int32"}, "townID": {"type": "integer", "format": "int32"}, "adress": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CompanyExerciseAddDto": {"type": "object", "properties": {"exerciseCategoryID": {"type": "integer", "format": "int32"}, "exerciseName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "muscleGroups": {"type": "string", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CompanyExerciseFilterDto": {"type": "object", "properties": {"exerciseCategoryID": {"type": "integer", "format": "int32", "nullable": true}, "searchTerm": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CompanyExerciseUpdateDto": {"type": "object", "properties": {"companyExerciseID": {"type": "integer", "format": "int32"}, "exerciseCategoryID": {"type": "integer", "format": "int32"}, "exerciseName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "muscleGroups": {"type": "string", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CompanyUser": {"type": "object", "properties": {"companyUserID": {"type": "integer", "format": "int32"}, "cityID": {"type": "integer", "format": "int32"}, "townID": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DebtPaymentDto": {"type": "object", "properties": {"remainingDebtID": {"type": "integer", "format": "int32"}, "paidAmount": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExerciseCategoryAddDto": {"type": "object", "properties": {"categoryName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExerciseCategoryUpdateDto": {"type": "object", "properties": {"exerciseCategoryID": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Expense": {"type": "object", "properties": {"expenseID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "expenseDate": {"type": "string", "format": "date-time"}, "expenseType": {"type": "string", "nullable": true}, "creationDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "LicensePackage": {"type": "object", "properties": {"licensePackageID": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "durationDays": {"type": "integer", "format": "int32"}, "price": {"type": "number", "format": "double"}, "isActive": {"type": "boolean"}, "creationDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LicensePurchaseDto": {"type": "object", "properties": {"userID": {"type": "integer", "format": "int32"}, "licensePackageID": {"type": "integer", "format": "int32"}, "paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LicenseTransaction": {"type": "object", "properties": {"licenseTransactionID": {"type": "integer", "format": "int32"}, "userID": {"type": "integer", "format": "int32"}, "licensePackageID": {"type": "integer", "format": "int32"}, "userLicenseID": {"type": "integer", "format": "int32", "nullable": true}, "amount": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "creationDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LoginRequestModel": {"type": "object", "properties": {"loginDto": {"$ref": "#/components/schemas/UserForLoginDto"}, "deviceInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Member": {"type": "object", "properties": {"memberID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "userID": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "gender": {"type": "integer", "format": "int32"}, "phoneNumber": {"type": "string", "nullable": true}, "adress": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "scanNumber": {"type": "string", "nullable": true}, "balance": {"type": "number", "format": "double"}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MemberForRegisterDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MemberProfileUpdateDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "adress": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date", "nullable": true}}, "additionalProperties": false}, "MemberRegisterRequestModel": {"type": "object", "properties": {"registerDto": {"$ref": "#/components/schemas/MemberForRegisterDto"}, "deviceInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MemberWorkoutProgramAddDto": {"type": "object", "properties": {"memberID": {"type": "integer", "format": "int32"}, "workoutProgramTemplateID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MemberWorkoutProgramUpdateDto": {"type": "object", "properties": {"memberWorkoutProgramID": {"type": "integer", "format": "int32"}, "workoutProgramTemplateID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "MembershipAddDto": {"type": "object", "properties": {"memberID": {"type": "integer", "format": "int32"}, "membershipTypeID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "paymentStatus": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}, "day": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MembershipFreezeHistory": {"type": "object", "properties": {"freezeHistoryID": {"type": "integer", "format": "int32"}, "membershipID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "plannedEndDate": {"type": "string", "format": "date-time"}, "actualEndDate": {"type": "string", "format": "date-time", "nullable": true}, "freezeDays": {"type": "integer", "format": "int32"}, "usedDays": {"type": "integer", "format": "int32", "nullable": true}, "cancellationType": {"type": "string", "nullable": true}, "creationDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MembershipFreezeRequestDto": {"type": "object", "properties": {"membershipID": {"type": "integer", "format": "int32"}, "freezeDays": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MembershipType": {"type": "object", "properties": {"membershipTypeID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "branch": {"type": "string", "nullable": true}, "typeName": {"type": "string", "nullable": true}, "day": {"type": "integer", "format": "int32"}, "price": {"type": "number", "format": "double"}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MembershipUpdateDto": {"type": "object", "properties": {"membershipID": {"type": "integer", "format": "int32"}, "membershipTypeID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "OperationClaim": {"type": "object", "properties": {"operationClaimId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Payment": {"type": "object", "properties": {"paymentID": {"type": "integer", "format": "int32"}, "memberShipID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentAmount": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}, "originalPaymentMethod": {"type": "string", "nullable": true}, "finalPaymentMethod": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "paymentStatus": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentUpdateModelDto": {"type": "object", "properties": {"paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Product": {"type": "object", "properties": {"productID": {"type": "integer", "format": "int32"}, "companyID": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "isActive": {"type": "boolean"}, "creationDate": {"type": "string", "format": "date-time"}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "RefreshTokenRequest": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}, "deviceInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterRequestModel": {"type": "object", "properties": {"registerDto": {"$ref": "#/components/schemas/UserForRegisterDto"}, "deviceInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ScanMemberForApiDto": {"type": "object", "properties": {"scanNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SystemExerciseAddDto": {"type": "object", "properties": {"exerciseCategoryID": {"type": "integer", "format": "int32"}, "exerciseName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "muscleGroups": {"type": "string", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "SystemExerciseFilterDto": {"type": "object", "properties": {"exerciseCategoryID": {"type": "integer", "format": "int32", "nullable": true}, "searchTerm": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SystemExerciseUpdateDto": {"type": "object", "properties": {"systemExerciseID": {"type": "integer", "format": "int32"}, "exerciseCategoryID": {"type": "integer", "format": "int32"}, "exerciseName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "muscleGroups": {"type": "string", "nullable": true}, "equipment": {"type": "string", "nullable": true}, "difficultyLevel": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Transaction": {"type": "object", "properties": {"transactionID": {"type": "integer", "format": "int32"}, "memberID": {"type": "integer", "format": "int32"}, "productID": {"type": "integer", "format": "int32", "nullable": true}, "companyID": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "transactionType": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "quantity": {"type": "integer", "format": "int32"}, "isPaid": {"type": "boolean"}}, "additionalProperties": false}, "TransactionItemDto": {"type": "object", "properties": {"productID": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "UserCompany": {"type": "object", "properties": {"userCompanyID": {"type": "integer", "format": "int32"}, "userID": {"type": "integer", "format": "int32"}, "companyId": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserForLoginDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserForRegisterDto": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserLicense": {"type": "object", "properties": {"userLicenseID": {"type": "integer", "format": "int32"}, "userID": {"type": "integer", "format": "int32"}, "licensePackageID": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "creationDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserOperationClaim": {"type": "object", "properties": {"userOperationClaimId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "operationClaimId": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "deletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WorkoutProgramDayAddDto": {"type": "object", "properties": {"dayNumber": {"type": "integer", "format": "int32"}, "dayName": {"type": "string", "nullable": true}, "isRestDay": {"type": "boolean"}, "exercises": {"type": "array", "items": {"$ref": "#/components/schemas/WorkoutProgramExerciseAddDto"}, "nullable": true}}, "additionalProperties": false}, "WorkoutProgramDayUpdateDto": {"type": "object", "properties": {"workoutProgramDayID": {"type": "integer", "format": "int32", "nullable": true}, "dayNumber": {"type": "integer", "format": "int32"}, "dayName": {"type": "string", "nullable": true}, "isRestDay": {"type": "boolean"}, "exercises": {"type": "array", "items": {"$ref": "#/components/schemas/WorkoutProgramExerciseUpdateDto"}, "nullable": true}}, "additionalProperties": false}, "WorkoutProgramExerciseAddDto": {"type": "object", "properties": {"exerciseType": {"type": "string", "nullable": true}, "exerciseID": {"type": "integer", "format": "int32"}, "orderIndex": {"type": "integer", "format": "int32"}, "sets": {"type": "integer", "format": "int32"}, "reps": {"type": "string", "nullable": true}, "restTime": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkoutProgramExerciseUpdateDto": {"type": "object", "properties": {"workoutProgramExerciseID": {"type": "integer", "format": "int32", "nullable": true}, "exerciseType": {"type": "string", "nullable": true}, "exerciseID": {"type": "integer", "format": "int32"}, "orderIndex": {"type": "integer", "format": "int32"}, "sets": {"type": "integer", "format": "int32"}, "reps": {"type": "string", "nullable": true}, "restTime": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkoutProgramTemplateAddDto": {"type": "object", "properties": {"programName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "experienceLevel": {"type": "string", "nullable": true}, "targetGoal": {"type": "string", "nullable": true}, "days": {"type": "array", "items": {"$ref": "#/components/schemas/WorkoutProgramDayAddDto"}, "nullable": true}}, "additionalProperties": false}, "WorkoutProgramTemplateUpdateDto": {"type": "object", "properties": {"workoutProgramTemplateID": {"type": "integer", "format": "int32"}, "programName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "experienceLevel": {"type": "string", "nullable": true}, "targetGoal": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "days": {"type": "array", "items": {"$ref": "#/components/schemas/WorkoutProgramDayUpdateDto"}, "nullable": true}}, "additionalProperties": false}, "WorkoutProgressResetDto": {"type": "object", "properties": {"memberWorkoutProgramID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkoutProgressToggleDto": {"type": "object", "properties": {"memberWorkoutProgramID": {"type": "integer", "format": "int32"}, "workoutProgramDayID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}